#!/bin/bash

os_ip=**************
os_user=root
os_passwd=tiangong@123

for i in $(seq 1 1); do
  time=$(date +"%Y-%m-%d %H:%M:%S")

expect << EOF
spawn ssh $os_user@$os_ip
expect {
    "fingerprint])?" {send "yes\r";exp_continue}
    "password:" {send "$os_passwd\r";exp_continue}
    "localhost ~]#" {send "echo -e '${time} ##################第${i}次升级##################\n' >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "rmmod iluvatar\r"}
}
expect {
    "localhost ~]#" {send "./ixfw-bi_v150-2.0.0_aarch64.run -l >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "echo '===============开始固件升级=============' >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "bash ixfw-bi_v150-2.0.6_aarch64.run >> /home/<USER>"}
}
# 等待固件升级菜单出现并选择Upgrade
expect {
    "Upgrade" {
        # 使用方向键选择Upgrade选项
        send "\033\[A"  # 向上箭头选择Upgrade
        sleep 0.5
        send "\r"       # 回车确认
        sleep 1
    }
    "Exit" {
        # 如果看到Exit，说明菜单已显示，向上选择Upgrade
        send "\033\[A"  # 向上箭头选择Upgrade
        sleep 0.5
        send "\r"       # 回车确认
        sleep 1
    }
    "FW_VERSION" {
        # 如果看到设备列表，等待一下然后选择Upgrade
        sleep 2
        send "\033\[A"  # 向上箭头选择Upgrade
        sleep 0.5
        send "\r"       # 回车确认
        sleep 1
    }
    timeout {
        # 超时情况下尝试选择Upgrade
        send "\033\[A"  # 向上箭头
        sleep 0.5
        send "\r"       # 回车确认
        sleep 1
    }
}
expect {
    "localhost ~]#" {send "reboot\r"}
}
expect eof
EOF

# 等待一段时间后检查OS是否已正常
sleep 300
while true; do
    sleep 2
    if ping $os_ip -c 2; then
      break
    fi
done

expect << EOF
spawn ssh $os_user@$os_ip
expect {
    "fingerprint])?" {send "yes\r";exp_continue}
    "password:" {send "$os_passwd\r";exp_continue}
    "localhost ~]#" {send "echo -e '${time} ##################第${i}次降级##################\n' >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "rmmod iluvatar\r"}
}
expect {
    "localhost ~]#" {send "./ixfw-bi_v150-2.0.0_aarch64.run -l >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "echo '===============开始固件降级=============' >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "bash ixfw-bi_v150-2.0.0_aarch64.run >> /home/<USER>"}
}
# 等待固件降级菜单出现并选择Upgrade
expect {
    "Upgrade" {
        # 使用方向键选择Upgrade选项
        send "\033\[A"  # 向上箭头选择Upgrade
        sleep 0.5
        send "\r"       # 回车确认
        sleep 1
    }
    "Exit" {
        # 如果看到Exit，说明菜单已显示，向上选择Upgrade
        send "\033\[A"  # 向上箭头选择Upgrade
        sleep 0.5
        send "\r"       # 回车确认
        sleep 1
    }
    "FW_VERSION" {
        # 如果看到设备列表，等待一下然后选择Upgrade
        sleep 2
        send "\033\[A"  # 向上箭头选择Upgrade
        sleep 0.5
        send "\r"       # 回车确认
        sleep 1
    }
    timeout {
        # 超时情况下尝试选择Upgrade
        send "\033\[A"  # 向上箭头
        sleep 0.5
        send "\r"       # 回车确认
        sleep 1
    }
}
expect {
    "localhost ~]#" {send "reboot\r"}
}
expect eof
EOF

sleep 300
while true; do
    sleep 2
    if ping $os_ip -c 2; then
      break
    fi
done

done