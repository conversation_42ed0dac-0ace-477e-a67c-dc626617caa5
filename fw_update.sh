#!/bin/bash

os_ip=**************
os_user=root
os_passwd=tiangong@123

for i in $(seq 1 10); do
  time=$(date +"%Y-%m-%d %H:%M:%S")

expect << EOF
spawn ssh $os_user@$os_ip
expect {
    "fingerprint])?" {send "yes\r";exp_continue}
    "password:" {send "$os_passwd\r";exp_continue}
    "localhost ~]#" {send "echo -e '${time} ##################第${i}次升级##################\n' >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "rmmod iluvatar\r"}
}
expect {
    "localhost ~]#" {send "./ixfw-bi_v150-2.0.0_aarch64.run -l >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "echo '===============开始固件升级=============' >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "bash ixfw-bi_v150-2.0.6_aarch64.run >> /home/<USER>"}
}
# 等待固件升级菜单出现并自动选择
expect {
    "Iluvatar" {
        # 发送向下箭头键选择选项
        for {set j 1} {\$j <= 14} {incr j} {
            send "\033\[B"
            sleep 0.1
        }
        send "\r"
    }
}
expect {
    "localhost ~]#" {send "reboot\r"}
}
expect eof
EOF

# 等待一段时间后检查OS是否已正常
sleep 300
while true; do
    sleep 2
    if ping $os_ip -c 2; then
      break
    fi
done

expect << EOF
spawn ssh $os_user@$os_ip
expect {
    "fingerprint])?" {send "yes\r";exp_continue}
    "password:" {send "$os_passwd\r";exp_continue}
    "localhost ~]#" {send "echo -e '${time} ##################第${i}次降级##################\n' >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "rmmod iluvatar\r"}
}
expect {
    "localhost ~]#" {send "./ixfw-bi_v150-2.0.0_aarch64.run -l >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "echo '===============开始固件降级=============' >> /home/<USER>"}
}
expect {
    "localhost ~]#" {send "bash ixfw-bi_v150-2.0.0_aarch64.run >> /home/<USER>"}
}
# 等待固件降级菜单出现并自动选择
expect {
    "Iluvatar" {
        # 发送向下箭头键选择选项
        for {set j 1} {\$j <= 14} {incr j} {
            send "\033\[B"
            sleep 0.1
        }
        send "\r"
    }
}
expect {
    "localhost ~]#" {send "reboot\r"}
}
expect eof
EOF

sleep 300
while true; do
    sleep 2
    if ping $os_ip -c 2; then
      break
    fi
done

done